import { importTypes } from '@rancher/auto-import';
import { IPlugin, OnNavToPackage, OnNavAwayFromPackage,ActionLocation, IInternal, PanelLocation } from '@shell/core/types';
import NotFound from "@shell/pages/404.vue"
import { EKSProvisioner } from './provisioner';
import extensionStore from './store';
import journeyStore from './store/journeys';
import extensionRouting from './routing/extension-routing';
import { explain } from './slide-in';


const onEnter: OnNavToPackage = async(store, config) => {
  console.log("on nav to",store,config)

  store.dispatch('saasAdmin/loadConfig')

  // Initialize journey system with demo mode enabled
  try {
    await store.dispatch('journeys/initializeJourneySystem', { demoMode: true });

    // Check for journey triggers on navigation
    await store.dispatch('journeys/checkTriggers', {
      event: 'navigation',
      context: { route: config.route, store }
    });
  } catch (error) {
    console.error('Failed to initialize journey system on navigation:', error);
  }
};
const onLeave: OnNavAwayFromPackage = async(store, config) => {
  console.log("on nav from",store,config)
};

export default function(plugin: IPlugin, internal: IInternal): void {
  importTypes(plugin);

  plugin.metadata = require('./package.json');

  plugin.addProduct(require('./product'));

  // Register custom provisioner object
  plugin.register('provisioner', EKSProvisioner.ID, EKSProvisioner);

  // plugin.addRoute({
  //   name:      'c-cluster-settings',
  //   path:      '/c/:cluster/settings',
  //   component: NotFound
  // });

  plugin.addRoutes(extensionRouting);
  plugin.addDashboardStore(extensionStore.config.namespace, extensionStore.specifics, extensionStore.config);
  plugin.addDashboardStore(journeyStore.config.namespace, journeyStore.specifics, journeyStore.config);
  plugin.addNavHooks(onEnter, onLeave);

  const store = internal.store;

  plugin.addAction(ActionLocation.HEADER, {}, {
    labelKey:   'qindex.action',
    tooltipKey: 'qindex.tooltip',
    svg:        require('./explain.svg'),
    invoke:     (opts, res, globals) => {
      explain(store, globals.$route);
    }
  });

  // Add PLG Mode Toggle Panel for CruResource (Advanced Mode)
  plugin.addPanel(
    PanelLocation.DETAILS_MASTHEAD,
    {
      resource: ['provisioning.cattle.io.cluster'],
      mode: ['create']
    },
    {
      component: () => import('./components/PLGModeToggle.vue')
    }
  );

}
