// definition of a "blank cluster" in Rancher Dashboard
import ListResource from '@shell/pages/c/_cluster/_product/_resource/index.vue';
import CreateResource from '@shell/pages/c/_cluster/_product/_resource/create.vue';
import ViewResource from '@shell/pages/c/_cluster/_product/_resource/_id.vue';
import ViewNamespacedResource from '@shell/pages/c/_cluster/_product/_resource/_namespace/_id.vue';
import InsightsPage from '../pages/insightsPage.vue';
import AuditlogPage from '../pages/auditLogPage.vue';
import DataExportPage from '../pages/dataExportPage.vue';
import KnowledgebasePage from '../pages/KnowledgeBase.vue';
import ConfigurationPage from '../pages/configurationPage.vue';
import JourneyDemoPage from '../pages/JourneyDemo.vue';
import ChatBot from '../components/KnowledgeBase.vue'

import {
  BLANK_CLUSTER, PRODUCT_NAME, AUDITLOG_PAGE_NAME, CUSTOM_PAGE_CONFIG, CUSTOM_PAGE_KNOWLEDGE, DATAEXPORT_PAGE_NAME,
  INSIGHTS_PAGE_NAME, JOURNEY_DEMO_PAGE_NAME
} from '../config';

const routes = [
  // this covers the "custom page"
  {
    name:      `${ PRODUCT_NAME }-c-cluster-${ AUDITLOG_PAGE_NAME }`,
    path:      `/${ PRODUCT_NAME }/c/:cluster/${ AUDITLOG_PAGE_NAME }`,
    component: AuditlogPage,
    meta:      {
      product: PRODUCT_NAME,
      cluster: BLANK_CLUSTER
    },
  },
  {
    name:      `${ PRODUCT_NAME }-c-cluster-${ CUSTOM_PAGE_CONFIG }`,
    path:      `/${ PRODUCT_NAME }/c/:cluster/${ CUSTOM_PAGE_CONFIG }`,
    component: ConfigurationPage,
    meta:      {
      product: PRODUCT_NAME,
      cluster: BLANK_CLUSTER
    },
  },
  {
    name:      `${ PRODUCT_NAME }-c-cluster-${ CUSTOM_PAGE_KNOWLEDGE }`,
    path:      `/${ PRODUCT_NAME }/c/:cluster/${ CUSTOM_PAGE_KNOWLEDGE }`,
    component: ChatBot,
    meta:      {
      product: PRODUCT_NAME,
      cluster: BLANK_CLUSTER
    },
  },
  {
    name:      `qauth`,
    path:      `/mcm/qauth/callback`,
    component: ChatBot,
    meta:      {
      product: PRODUCT_NAME,
      cluster: BLANK_CLUSTER
    },
  },
  {
    name:      `${ PRODUCT_NAME }-c-cluster-${ DATAEXPORT_PAGE_NAME }`,
    path:      `/${ PRODUCT_NAME }/c/:cluster/${ DATAEXPORT_PAGE_NAME }`,
    component: DataExportPage,
    meta:      {
      product: PRODUCT_NAME,
      cluster: BLANK_CLUSTER
    },
  },
  {
    name:      `${ PRODUCT_NAME }-c-cluster-${ INSIGHTS_PAGE_NAME }`,
    path:      `/${ PRODUCT_NAME }/c/:cluster/${ INSIGHTS_PAGE_NAME }`,
    component: InsightsPage,
    meta:      {
      product: PRODUCT_NAME,
      cluster: BLANK_CLUSTER
    },
  },
  {
    name:      `${ PRODUCT_NAME }-c-cluster-${ JOURNEY_DEMO_PAGE_NAME }`,
    path:      `/${ PRODUCT_NAME }/c/:cluster/${ JOURNEY_DEMO_PAGE_NAME }`,
    component: JourneyDemoPage,
    meta:      {
      product: PRODUCT_NAME,
      cluster: BLANK_CLUSTER
    },
  },

];

export default routes;
