<template>
  <div class="journey-demo">
    <!-- <PERSON>er -->
    <div class="header">
      <h1>PLG Journey System Demo</h1>
      <p>Demonstration of the Product-Led Growth journey system with manual triggers and state monitoring.</p>
    </div>

    <!-- System State Display -->
    <div class="section">
      <h2>Current System State</h2>
      <div class="state-grid">
        <div class="state-card">
          <h3>Journey System</h3>
          <div class="state-item">
            <span class="label">Status:</span>
            <span :class="['value', isLoading ? 'loading' : 'active']">
              {{ isLoading ? 'Loading...' : 'Active' }}
            </span>
          </div>
          <div class="state-item">
            <span class="label">Definitions Loaded:</span>
            <span class="value">{{ Object.keys(definitions).length }}</span>
          </div>
          <div class="state-item">
            <span class="label">Current Journey:</span>
            <span class="value">{{ currentJourney?.name || 'None' }}</span>
          </div>
        </div>

        <div class="state-card">
          <h3>User Context</h3>
          <div class="state-item">
            <span class="label">First Login:</span>
            <span class="value">{{ systemState.isFirstLogin ? 'Yes' : 'No' }}</span>
          </div>
          <div class="state-item">
            <span class="label">Clusters:</span>
            <span class="value">{{ systemState.clusterCount }}</span>
          </div>
          <div class="state-item">
            <span class="label">Credentials:</span>
            <span class="value">{{ systemState.credentialCount }}</span>
          </div>
          <div class="state-item">
            <span class="label">PLG Mode:</span>
            <span class="value">{{ systemState.isPLGMode ? 'Enabled' : 'Disabled' }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Available Journeys -->
    <div class="section">
      <h2>Available Journeys</h2>
      <div class="journey-grid">
        <div 
          v-for="journey in availableJourneys" 
          :key="journey.id" 
          class="journey-card"
        >
          <div class="journey-header">
            <h3>{{ journey.name }}</h3>
            <span class="journey-category">{{ journey.category }}</span>
          </div>
          <p class="journey-description">{{ journey.description }}</p>
          
          <div class="journey-meta">
            <span class="duration">{{ journey.estimatedDuration || 'N/A' }} min</span>
            <span class="difficulty">{{ journey.difficulty || 'beginner' }}</span>
            <span class="steps">{{ journey.steps.length }} steps</span>
          </div>

          <div class="journey-actions">
            <button 
              class="btn btn-sm role-primary"
              :disabled="isLoading || isJourneyInProgress(journey.id)"
              @click="startJourney(journey.id)"
            >
              {{ isJourneyInProgress(journey.id) ? 'In Progress' : 'Start Journey' }}
            </button>
            
            <button 
              v-if="isJourneyInProgress(journey.id)"
              class="btn btn-sm role-secondary"
              @click="resumeJourney(journey.id)"
            >
              Resume
            </button>
          </div>

          <!-- Progress indicator if journey is in progress -->
          <div v-if="isJourneyInProgress(journey.id)" class="progress-indicator">
            <div class="progress-bar">
              <div 
                class="progress-fill" 
                :style="{ width: getCompletionPercentage(journey.id) + '%' }"
              ></div>
            </div>
            <span class="progress-text">{{ Math.round(getCompletionPercentage(journey.id)) }}% complete</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Demo Actions -->
    <div class="section">
      <h2>Demo Actions</h2>
      <div class="demo-actions">
        <button 
          class="btn role-secondary"
          :disabled="isLoading"
          @click="simulateFirstLogin"
        >
          Simulate First Login
        </button>
        
        <button 
          class="btn role-secondary"
          :disabled="isLoading"
          @click="simulateClusterCreation"
        >
          Simulate Cluster Creation
        </button>
        
        <button 
          class="btn role-tertiary"
          :disabled="isLoading"
          @click="resetDemoState"
        >
          Reset Demo State
        </button>
        
        <button 
          class="btn role-tertiary"
          :disabled="isLoading"
          @click="clearAllProgress"
        >
          Clear All Progress
        </button>
      </div>
    </div>

    <!-- Current Journey Display -->
    <div v-if="currentJourney" class="section">
      <h2>Current Journey: {{ currentJourney.name }}</h2>
      <div class="current-journey">
        <ProgressTracker
          :current-step="currentStepIndex + 1"
          :total-steps="currentJourney.steps.length"
          :journey="currentJourney"
          :progress="currentProgress"
          :show-step-indicators="true"
          :show-restart-button="true"
          @restart-journey="restartCurrentJourney"
        />
        
        <div class="journey-controls">
          <button 
            class="btn role-secondary"
            :disabled="!canGoPrevious"
            @click="previousStep"
          >
            Previous
          </button>
          
          <button 
            class="btn role-primary"
            :disabled="!canGoNext"
            @click="nextStep"
          >
            Next
          </button>
          
          <button 
            class="btn role-tertiary"
            @click="skipJourney"
          >
            Skip Journey
          </button>
        </div>
      </div>
    </div>

    <!-- Journey Wizard Modal -->
    <JourneyWizard
      v-if="showJourneyWizard && currentJourney"
      :journey-id="currentJourney.id"
      @journey-completed="handleJourneyCompleted"
      @journey-skipped="handleJourneySkipped"
      @journey-error="handleJourneyError"
    />
  </div>
</template>

<script lang="ts">
import { defineComponent, computed, ref } from 'vue';
import { mapGetters, mapActions } from 'vuex';
import ProgressTracker from '../components/journey/ProgressTracker.vue';
import JourneyWizard from '../components/journey/JourneyWizard.vue';

export default defineComponent({
  name: 'JourneyDemo',

  components: {
    ProgressTracker,
    JourneyWizard,
  },

  setup() {
    const showJourneyWizard = ref(false);

    return {
      showJourneyWizard,
    };
  },

  computed: {
    // Journey store getters with fallbacks
    definitions() {
      return this.$store.getters['journeys/definitions'] || {};
    },
    currentJourney() {
      return this.$store.getters['journeys/currentJourney'] || null;
    },
    currentProgress() {
      return this.$store.getters['journeys/currentProgress'] || null;
    },
    systemState() {
      return this.$store.getters['journeys/systemState'] || {};
    },
    isLoading() {
      return this.$store.getters['journeys/isLoading'] || false;
    },
    availableJourneys() {
      return this.$store.getters['journeys/availableJourneys'] || [];
    },
    currentStepIndex() {
      return this.$store.getters['journeys/currentStepIndex'] || 0;
    },
    canGoNext() {
      return this.$store.getters['journeys/canGoNext'] || false;
    },
    canGoPrevious() {
      return this.$store.getters['journeys/canGoPrevious'] || false;
    },
    getCompletionPercentage() {
      return this.$store.getters['journeys/getCompletionPercentage'] || (() => 0);
    },
    isJourneyInProgress() {
      return this.$store.getters['journeys/isJourneyInProgress'] || (() => false);
    },
  },

  methods: {
    // Journey store actions
    async triggerJourneyManually(payload) {
      return this.$store.dispatch('journeys/triggerJourneyManually', payload);
    },
    async simulateFirstLogin() {
      return this.$store.dispatch('journeys/simulateFirstLogin');
    },
    async simulateClusterCreation(payload) {
      return this.$store.dispatch('journeys/simulateClusterCreation', payload);
    },
    async setupDemoSystemState() {
      return this.$store.dispatch('journeys/setupDemoSystemState');
    },
    async clearAllJourneyData() {
      return this.$store.dispatch('journeys/clearAllJourneyData');
    },
    async resumeJourney(journeyId) {
      return this.$store.dispatch('journeys/resumeJourney', journeyId);
    },
    async nextStep() {
      return this.$store.dispatch('journeys/nextStep');
    },
    async previousStep() {
      return this.$store.dispatch('journeys/previousStep');
    },
    async skipJourney() {
      return this.$store.dispatch('journeys/skipJourney');
    },
    async completeJourney() {
      return this.$store.dispatch('journeys/completeJourney');
    },

    async startJourney(journeyId: string) {
      try {
        await this.triggerJourneyManually({ journeyId });
        this.showJourneyWizard = true;
      } catch (error) {
        console.error('Failed to start journey:', error);
        this.$store.dispatch('growl/error', {
          title: 'Journey Error',
          message: `Failed to start journey: ${error.message}`,
        });
      }
    },

    async resetDemoState() {
      try {
        await this.setupDemoSystemState();
        this.$store.dispatch('growl/success', {
          title: 'Demo Reset',
          message: 'Demo system state has been reset',
        });
      } catch (error) {
        console.error('Failed to reset demo state:', error);
      }
    },

    async clearAllProgress() {
      try {
        await this.clearAllJourneyData();
        this.showJourneyWizard = false;
        this.$store.dispatch('growl/success', {
          title: 'Progress Cleared',
          message: 'All journey progress has been cleared',
        });
      } catch (error) {
        console.error('Failed to clear progress:', error);
      }
    },

    async restartCurrentJourney() {
      if (this.currentJourney) {
        await this.startJourney(this.currentJourney.id);
      }
    },

    handleJourneyCompleted(event: any) {
      this.showJourneyWizard = false;
      this.$store.dispatch('growl/success', {
        title: 'Journey Completed',
        message: `Successfully completed: ${event.journey?.name}`,
      });
    },

    handleJourneySkipped(event: any) {
      this.showJourneyWizard = false;
      this.$store.dispatch('growl/info', {
        title: 'Journey Skipped',
        message: `Skipped: ${event.journey?.name}`,
      });
    },

    handleJourneyError(event: any) {
      this.showJourneyWizard = false;
      this.$store.dispatch('growl/error', {
        title: 'Journey Error',
        message: `Error in journey: ${event.error?.message}`,
      });
    },
  },
});
</script>

<style lang="scss" scoped>
.journey-demo {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;

  .header {
    margin-bottom: 30px;
    text-align: center;

    h1 {
      color: var(--primary);
      margin-bottom: 10px;
    }

    p {
      color: var(--muted);
      font-size: 16px;
    }
  }

  .section {
    margin-bottom: 40px;
    padding: 20px;
    background: var(--box-bg);
    border: 1px solid var(--border);
    border-radius: 8px;

    h2 {
      margin-bottom: 20px;
      color: var(--body-text);
      border-bottom: 2px solid var(--primary);
      padding-bottom: 10px;
    }
  }

  .state-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
  }

  .state-card {
    padding: 15px;
    background: var(--body-bg);
    border: 1px solid var(--border);
    border-radius: 6px;

    h3 {
      margin-bottom: 15px;
      color: var(--primary);
    }

    .state-item {
      display: flex;
      justify-content: space-between;
      margin-bottom: 8px;

      .label {
        font-weight: 600;
        color: var(--body-text);
      }

      .value {
        color: var(--muted);

        &.loading {
          color: var(--warning);
        }

        &.active {
          color: var(--success);
        }
      }
    }
  }

  .journey-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 20px;
  }

  .journey-card {
    padding: 20px;
    background: var(--body-bg);
    border: 1px solid var(--border);
    border-radius: 8px;
    transition: box-shadow 0.2s ease;

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .journey-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;

      h3 {
        margin: 0;
        color: var(--primary);
      }

      .journey-category {
        background: var(--primary);
        color: white;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        text-transform: uppercase;
      }
    }

    .journey-description {
      color: var(--muted);
      margin-bottom: 15px;
      line-height: 1.4;
    }

    .journey-meta {
      display: flex;
      gap: 10px;
      margin-bottom: 15px;

      span {
        background: var(--box-bg);
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        color: var(--muted);
      }
    }

    .journey-actions {
      display: flex;
      gap: 10px;
      margin-bottom: 15px;
    }

    .progress-indicator {
      .progress-bar {
        width: 100%;
        height: 6px;
        background: var(--border);
        border-radius: 3px;
        overflow: hidden;
        margin-bottom: 5px;

        .progress-fill {
          height: 100%;
          background: var(--primary);
          transition: width 0.3s ease;
        }
      }

      .progress-text {
        font-size: 12px;
        color: var(--muted);
      }
    }
  }

  .demo-actions {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
  }

  .current-journey {
    .journey-controls {
      display: flex;
      gap: 10px;
      margin-top: 20px;
      justify-content: center;
    }
  }
}
</style>
