import type {
  JourneyState,
  JourneyDefinition,
  JourneyProgress,
  JourneyAction,
  JourneyContext,
  JourneyEventPayload
} from '../../types/journey';
import { create } from '@shell/store/prefs';

// Define journey preferences following <PERSON><PERSON>'s patterns
export const JOURNEY_PREFERENCES = {
  // Journey progress tracking
  PROGRESS: create('journey-progress', {}, {
    parseJSON: true,
    asUserPreference: true,
    mangleNames: false,
  }),

  // Journey configuration settings
  CONFIG: create('journey-config', {
    enableJourneys: true,
    enableAnalytics: true,
    debugMode: false,
    autoTrigger: true,
    tourMode: false,
  }, {
    parseJSON: true,
    asUserPreference: true,
    mangleNames: false,
  }),

  // Journey analytics data
  ANALYTICS: create('journey-analytics', {
    journeyStartCount: {},
    journeyCompletionCount: {},
    journeySkipCount: {},
    averageCompletionTime: {},
    dropOffPoints: {},
    popularChoices: {},
    userPaths: [],
  }, {
    parseJSON: true,
    asUserPreference: true,
    mangleNames: false,
  }),

  // System state for journey triggering
  SYSTEM_STATE: create('journey-system-state', {
    lastLogin: null,
    isFirstLogin: false,
    hasCompletedOnboarding: false,
    clusterCount: 0,
    credentialCount: 0,
    applicationCount: 0,
  }, {
    parseJSON: true,
    asUserPreference: true,
    mangleNames: false,
  }),

  // Completed journeys list
  COMPLETED_JOURNEYS: create('journey-completed', [], {
    parseJSON: true,
    asUserPreference: true,
    mangleNames: false,
  }),

  // UI state preferences
  UI_STATE: create('journey-ui-state', {
    showJourneyOverlay: false,
    overlayPosition: 'center',
    minimized: false,
    lastInteraction: null,
  }, {
    parseJSON: true,
    asUserPreference: true,
    mangleNames: false,
  }),
} as const;

export default {
  // Main Journey System Initialization
  async initializeJourneySystem({ dispatch, commit, state }: any, { demoMode = false } = {}) {
    try {
      console.log('Initializing PLG Journey System...', { demoMode });
      commit('SET_JOURNEY_LOADING', true);

      // 1. Load journey definitions first
      await dispatch('loadJourneyDefinitions');

      // 2. Initialize from user preferences (existing progress, config, etc.)
      await dispatch('initializeFromPreferences');

      // 3. Set up demo system state if in demo mode
      if (demoMode) {
        await dispatch('setupDemoSystemState');
      }

      // 4. Update system state based on current Rancher state
      await dispatch('updateSystemStateFromRancher');

      console.log('PLG Journey System initialized successfully');
      return true;
    } catch (error) {
      console.error('Failed to initialize PLG Journey System:', error);
      commit('ADD_ERROR', error);
      throw error;
    } finally {
      commit('SET_JOURNEY_LOADING', false);
    }
  },

  // Setup Demo System State for Testing
  async setupDemoSystemState({ commit }: any) {
    console.log('Setting up demo system state...');

    const demoSystemState = {
      lastLogin: new Date(),
      isFirstLogin: true, // Enable first login journey
      hasCompletedOnboarding: false,
      clusterCount: 0, // No clusters initially
      credentialCount: 0, // No credentials initially
      applicationCount: 0,
      userRole: 'admin',
      activeFeatures: ['cluster-management', 'app-deployment'],
      isPLGMode: true, // Enable PLG mode
      lastCreatedResource: null,
      clusters: [],
      credentials: [],
      users: [],
      applications: [],
    };

    commit('SET_SYSTEM_STATE', demoSystemState);

    // Also save to preferences for persistence
    await this.dispatch('prefs/set', {
      key: JOURNEY_PREFERENCES.SYSTEM_STATE,
      value: demoSystemState
    }, { root: true });

    console.log('Demo system state configured:', demoSystemState);
  },

  // Update System State from Current Rancher State
  async updateSystemStateFromRancher({ commit, rootGetters }: any) {
    try {
      console.log('Updating system state from Rancher...');

      // Get current clusters, credentials, etc. from Rancher stores
      const clusters = rootGetters['management/all']?.('management.cattle.io.cluster') || [];
      const credentials = rootGetters['management/all']?.('provisioning.cattle.io.cluster') || [];

      const systemStateUpdate = {
        clusterCount: clusters.length,
        credentialCount: credentials.length,
        clusters: clusters.map((c: any) => ({ id: c.id, name: c.nameDisplay, state: c.state })),
        credentials: credentials.map((c: any) => ({ id: c.id, name: c.nameDisplay, type: c.spec?.rkeConfig?.machineGlobalConfig?.cloud_provider })),
      };

      commit('UPDATE_SYSTEM_STATE', systemStateUpdate);
      console.log('System state updated from Rancher:', systemStateUpdate);
    } catch (error) {
      console.warn('Failed to update system state from Rancher:', error);
      // Don't throw - this is not critical for demo
    }
  },

  // Journey Management Actions
  async loadJourneyDefinitions({ commit, state }: any) {
    try {
      commit('SET_JOURNEY_LOADING', true);

      // Load journey definitions from configuration
      // This could be from API, local files, or embedded definitions
      const { default: journeyDefinitions } = await import('./definitions');

      commit('SET_JOURNEY_DEFINITIONS', journeyDefinitions);
      commit('CLEAR_CACHE'); // Clear cache when definitions change

      console.log('Journey definitions loaded:', Object.keys(journeyDefinitions));
    } catch (error) {
      commit('ADD_ERROR', error);
      throw error;
    } finally {
      commit('SET_JOURNEY_LOADING', false);
    }
  },

  async startJourney({ commit, dispatch, state, getters }: any, { journeyId, context }: { journeyId: string, context?: Partial<JourneyContext> }) {
    try {
      const journey = getters.getJourneyById(journeyId);
      if (!journey) {
        throw new Error(`Journey not found: ${journeyId}`);
      }

      // Check if journey is already in progress
      const existingProgress = getters.getProgressById(journeyId);
      if (existingProgress?.status === 'in-progress') {
        return dispatch('resumeJourney', journeyId);
      }

      // Create new progress
      const progress: JourneyProgress = {
        journeyId,
        status: 'in-progress',
        currentStepId: journey.steps[0]?.id,
        currentStepIndex: 0,
        completedSteps: [],
        decisions: {},
        startedAt: new Date(),
        lastActiveAt: new Date(),
        metadata: context || {},
      };

      // Set current journey and progress
      commit('SET_CURRENT_JOURNEY', journey);
      commit('SET_CURRENT_PROGRESS', progress);
      commit('SET_USER_PROGRESS', { journeyId, progress });
      commit('SET_JOURNEY_ACTIVE', true);

      // Save progress to user preferences
      await dispatch('saveToUserPreferences', progress);

      // Analytics
      commit('INCREMENT_JOURNEY_START_COUNT', journeyId);

      // Save analytics data
      await dispatch('saveJourneyAnalytics', {
        journeyStartCount: { [journeyId]: (state.analytics?.journeyStartCount?.[journeyId] || 0) + 1 },
      });

      // Execute journey start hook
      if (journey.onStart) {
        const journeyContext = await dispatch('buildJourneyContext', { journey, progress, context });
        await journey.onStart(journeyContext);
      }

      // Emit event
      dispatch('emitJourneyEvent', {
        event: 'journey-started',
        journeyId,
        data: { journey, progress },
      });

      return progress;

    } catch (error) {
      commit('ADD_ERROR', error);
      throw error;
    }
  },

  async resumeJourney({ commit, dispatch, state, getters }: any, journeyId: string) {
    try {
      const journey = getters.getJourneyById(journeyId);
      const progress = getters.getProgressById(journeyId);

      if (!journey || !progress) {
        throw new Error(`Cannot resume journey: ${journeyId}`);
      }

      if (progress.status !== 'in-progress') {
        throw new Error(`Journey is not in progress: ${journeyId}`);
      }

      // Set as current journey
      commit('SET_CURRENT_JOURNEY', journey);
      commit('SET_CURRENT_PROGRESS', progress);
      commit('SET_JOURNEY_ACTIVE', true);

      // Update last active time
      commit('UPDATE_USER_PROGRESS', { 
        journeyId, 
        updates: { lastActiveAt: new Date() } 
      });

      return progress;

    } catch (error) {
      commit('ADD_ERROR', error);
      throw error;
    }
  },

  async completeJourney({ commit, dispatch, state, getters }: any, journeyId: string) {
    try {
      const journey = getters.getJourneyById(journeyId);
      const progress = getters.getProgressById(journeyId);

      if (!journey || !progress) {
        throw new Error(`Cannot complete journey: ${journeyId}`);
      }

      const completedAt = new Date();
      const duration = progress.startedAt ? 
        Math.floor((completedAt.getTime() - new Date(progress.startedAt).getTime()) / (1000 * 60)) : 0;

      // Update progress
      const updatedProgress = {
        ...progress,
        status: 'completed' as const,
        completedAt,
        lastActiveAt: completedAt,
      };

      commit('SET_USER_PROGRESS', { journeyId, progress: updatedProgress });
      commit('ADD_JOURNEY_HISTORY', updatedProgress);

      // Analytics
      commit('INCREMENT_JOURNEY_COMPLETION_COUNT', journeyId);
      commit('UPDATE_AVERAGE_COMPLETION_TIME', { journeyId, duration });
      commit('ADD_USER_PATH', {
        journeyId,
        steps: progress.completedSteps,
        decisions: progress.decisions,
        duration,
      });

      // Execute journey completion hook
      if (journey.onComplete) {
        const journeyContext = await dispatch('buildJourneyContext', { journey, progress: updatedProgress });
        await journey.onComplete(journeyContext);
      }

      // Save progress to user preferences
      await dispatch('saveToUserPreferences', updatedProgress);

      // Add to completed journeys list
      const completedJourneys = await dispatch('loadCompletedJourneys');
      if (!completedJourneys.includes(journeyId)) {
        completedJourneys.push(journeyId);
        await dispatch('saveCompletedJourneys', completedJourneys);
      }

      // Save analytics data
      await dispatch('saveJourneyAnalytics', {
        journeyCompletionCount: { [journeyId]: (state.analytics?.journeyCompletionCount?.[journeyId] || 0) + 1 },
        averageCompletionTime: { [journeyId]: duration },
        userPaths: [{
          journeyId,
          steps: progress.completedSteps,
          decisions: progress.decisions,
          duration,
          completedAt,
        }],
      });

      // Clear current journey if it's the one being completed
      if (state.currentJourney?.id === journeyId) {
        commit('SET_CURRENT_JOURNEY', null);
        commit('SET_CURRENT_PROGRESS', null);
        commit('SET_JOURNEY_ACTIVE', false);
      }

      // Emit event
      dispatch('emitJourneyEvent', {
        event: 'journey-completed',
        journeyId,
        data: { journey, progress: updatedProgress, duration },
      });

      return updatedProgress;

    } catch (error) {
      commit('ADD_ERROR', error);
      throw error;
    }
  },

  async skipJourney({ commit, dispatch, state, getters }: any, journeyId: string) {
    try {
      const journey = getters.getJourneyById(journeyId);
      const progress = getters.getProgressById(journeyId);

      if (!journey || !progress) {
        throw new Error(`Cannot skip journey: ${journeyId}`);
      }

      const skippedAt = new Date();
      const updatedProgress = {
        ...progress,
        status: 'skipped' as const,
        completedAt: skippedAt,
        lastActiveAt: skippedAt,
      };

      commit('SET_USER_PROGRESS', { journeyId, progress: updatedProgress });
      commit('ADD_JOURNEY_HISTORY', updatedProgress);

      // Save progress to user preferences
      await dispatch('saveToUserPreferences', updatedProgress);

      // Analytics
      commit('INCREMENT_JOURNEY_SKIP_COUNT', journeyId);
      if (progress.currentStepId) {
        commit('ADD_DROP_OFF_POINT', { journeyId, stepId: progress.currentStepId });
      }

      // Save analytics data
      await dispatch('saveJourneyAnalytics', {
        journeySkipCount: { [journeyId]: (state.analytics?.journeySkipCount?.[journeyId] || 0) + 1 },
        dropOffPoints: progress.currentStepId ? {
          [`${journeyId}-${progress.currentStepId}`]: (state.analytics?.dropOffPoints?.[`${journeyId}-${progress.currentStepId}`] || 0) + 1
        } : {},
      });

      // Execute journey skip hook
      if (journey.onSkip) {
        const journeyContext = await dispatch('buildJourneyContext', { journey, progress: updatedProgress });
        await journey.onSkip(journeyContext);
      }

      // Clear current journey if it's the one being skipped
      if (state.currentJourney?.id === journeyId) {
        commit('SET_CURRENT_JOURNEY', null);
        commit('SET_CURRENT_PROGRESS', null);
        commit('SET_JOURNEY_ACTIVE', false);
      }

      // Emit event
      dispatch('emitJourneyEvent', {
        event: 'journey-skipped',
        journeyId,
        data: { journey, progress: updatedProgress },
      });

      return updatedProgress;

    } catch (error) {
      commit('ADD_ERROR', error);
      throw error;
    }
  },

  async restartJourney({ dispatch }: any, journeyId: string) {
    try {
      // Skip current journey if in progress
      await dispatch('skipJourney', journeyId);
      
      // Start fresh
      await dispatch('startJourney', { journeyId });

    } catch (error) {
      throw error;
    }
  },

  // Step Navigation Actions
  async nextStep({ commit, dispatch, state, getters }: any) {
    try {
      if (!state.currentJourney || !state.currentProgress) {
        throw new Error('No active journey');
      }

      const nextStep = getters.getNextStep;
      if (!nextStep) {
        // Journey is complete
        return dispatch('completeJourney', state.currentJourney.id);
      }

      const currentStepIndex = getters.getCurrentStepIndex;
      const newStepIndex = currentStepIndex + 1;

      // Mark current step as completed
      if (state.currentProgress.currentStepId) {
        commit('ADD_COMPLETED_STEP', {
          journeyId: state.currentJourney.id,
          stepId: state.currentProgress.currentStepId,
        });
      }

      // Move to next step
      commit('SET_CURRENT_STEP', {
        journeyId: state.currentJourney.id,
        stepId: nextStep.id,
        stepIndex: newStepIndex,
      });

      // Save progress
      await dispatch('saveProgress');

      // Emit event
      dispatch('emitJourneyEvent', {
        event: 'step-entered',
        journeyId: state.currentJourney.id,
        stepId: nextStep.id,
        data: { step: nextStep, stepIndex: newStepIndex },
      });

    } catch (error) {
      commit('ADD_ERROR', error);
      throw error;
    }
  },

  async previousStep({ commit, dispatch, state, getters }: any) {
    try {
      if (!state.currentJourney || !state.currentProgress) {
        throw new Error('No active journey');
      }

      const previousStep = getters.getPreviousStep;
      if (!previousStep) {
        throw new Error('No previous step available');
      }

      const currentStepIndex = getters.getCurrentStepIndex;
      const newStepIndex = currentStepIndex - 1;

      // Move to previous step
      commit('SET_CURRENT_STEP', {
        journeyId: state.currentJourney.id,
        stepId: previousStep.id,
        stepIndex: newStepIndex,
      });

      // Save progress
      await dispatch('saveProgress');

      // Emit event
      dispatch('emitJourneyEvent', {
        event: 'step-entered',
        journeyId: state.currentJourney.id,
        stepId: previousStep.id,
        data: { step: previousStep, stepIndex: newStepIndex },
      });

    } catch (error) {
      commit('ADD_ERROR', error);
      throw error;
    }
  },

  async goToStep({ commit, dispatch, state, getters }: any, stepId: string) {
    try {
      if (!state.currentJourney || !state.currentProgress) {
        throw new Error('No active journey');
      }

      const step = state.currentJourney.steps.find((s: any) => s.id === stepId);
      if (!step) {
        throw new Error(`Step not found: ${stepId}`);
      }

      const stepIndex = state.currentJourney.steps.indexOf(step);

      // Move to specified step
      commit('SET_CURRENT_STEP', {
        journeyId: state.currentJourney.id,
        stepId: step.id,
        stepIndex,
      });

      // Save progress
      await dispatch('saveProgress');

      // Emit event
      dispatch('emitJourneyEvent', {
        event: 'step-entered',
        journeyId: state.currentJourney.id,
        stepId: step.id,
        data: { step, stepIndex },
      });

    } catch (error) {
      commit('ADD_ERROR', error);
      throw error;
    }
  },

  // Decision Handling
  async makeDecision({ commit, dispatch, state }: any, { stepId, choiceId, data }: { stepId: string, choiceId: string, data?: any }) {
    try {
      if (!state.currentJourney || !state.currentProgress) {
        throw new Error('No active journey');
      }

      // Record the decision
      commit('ADD_DECISION', {
        journeyId: state.currentJourney.id,
        stepId,
        choiceId,
        data,
      });

      // Analytics
      commit('RECORD_CHOICE_SELECTION', {
        journeyId: state.currentJourney.id,
        stepId,
        choiceId,
      });

      // Save analytics data for popular choices
      await dispatch('saveJourneyAnalytics', {
        popularChoices: {
          [`${state.currentJourney.id}-${stepId}-${choiceId}`]: (state.analytics?.popularChoices?.[`${state.currentJourney.id}-${stepId}-${choiceId}`] || 0) + 1
        },
      });

      // Save progress
      await dispatch('saveProgress');

      // Emit event
      dispatch('emitJourneyEvent', {
        event: 'decision-made',
        journeyId: state.currentJourney.id,
        stepId,
        data: { choiceId, data },
      });

      // Handle choice navigation
      const step = state.currentJourney.steps.find((s: any) => s.id === stepId);
      const choice = step?.choices?.find((c: any) => c.id === choiceId);

      if (choice?.next) {
        await dispatch('goToStep', choice.next);
      } else if (choice?.action) {
        await dispatch('executeAction', { action: choice.action, context: { choice, step } });
      }

    } catch (error) {
      commit('ADD_ERROR', error);
      throw error;
    }
  },

  // Workflow Execution
  async executeAction({ commit, dispatch, state, rootState }: any, { action, context }: { action: JourneyAction, context: JourneyContext }) {
    try {
      let result;

      switch (action.type) {
        case 'navigate':
          result = await dispatch('executeNavigateAction', { action, context }, { root: true });
          break;
        case 'create-resource':
          result = await dispatch('executeCreateResourceAction', { action, context }, { root: true });
          break;
        case 'update-resource':
          result = await dispatch('executeUpdateResourceAction', { action, context }, { root: true });
          break;
        case 'trigger-workflow':
          result = await dispatch('executeTriggerWorkflowAction', { action, context }, { root: true });
          break;
        case 'show-modal':
          result = await dispatch('executeShowModalAction', { action, context }, { root: true });
          break;
        case 'custom':
          if (action.customHandler) {
            result = await action.customHandler(context);
          } else {
            throw new Error('Custom action handler not defined');
          }
          break;
        default:
          throw new Error(`Unknown action type: ${action.type}`);
      }

      // Emit event
      dispatch('emitJourneyEvent', {
        event: 'action-executed',
        journeyId: state.currentJourney?.id,
        data: { action, result, context },
      });

      return result;

    } catch (error) {
      commit('ADD_ERROR', error);
      throw error;
    }
  },

  // Progress Management
  async updateProgress({ commit }: any, { journeyId, progress }: { journeyId: string, progress: Partial<JourneyProgress> }) {
    commit('UPDATE_USER_PROGRESS', { journeyId, updates: progress });
  },

  async saveProgress({ state, dispatch }: any) {
    try {
      if (!state.currentProgress) return;

      // Save to user preferences API
      await dispatch('saveToUserPreferences', state.currentProgress);

    } catch (error) {
      // Fallback to localStorage
      await dispatch('saveToLocalStorage', state.currentProgress);
    }
  },

  async loadProgress({ commit, dispatch }: any) {
    try {
      // Try to load from user preferences API first
      const progress = await dispatch('loadFromUserPreferences');
      if (progress) {
        Object.keys(progress).forEach(journeyId => {
          commit('SET_USER_PROGRESS', { journeyId, progress: progress[journeyId] });
        });
      }
    } catch (error) {
      // Fallback to localStorage
      const progress = await dispatch('loadFromLocalStorage');
      if (progress) {
        Object.keys(progress).forEach(journeyId => {
          commit('SET_USER_PROGRESS', { journeyId, progress: progress[journeyId] });
        });
      }
    }
  },

  async saveToUserPreferences({ dispatch, rootGetters }: any, progress: JourneyProgress) {
    try {
      // Load existing progress from preferences
      const currentProgress = rootGetters['prefs/get'](JOURNEY_PREFERENCES.PROGRESS) || {};

      // Update with new progress data
      currentProgress[progress.journeyId] = {
        ...progress,
        lastActiveAt: new Date(),
      };

      // Save via prefs store
      await dispatch('prefs/set', {
        key: JOURNEY_PREFERENCES.PROGRESS,
        value: currentProgress
      }, { root: true });

      console.log(`Journey progress saved for: ${progress.journeyId}`);
    } catch (error) {
      console.error('Failed to save journey progress to user preferences:', error);
      throw error;
    }
  },

  async loadFromUserPreferences({ rootGetters }: any) {
    try {
      // Load progress from user preferences
      const progress = rootGetters['prefs/get'](JOURNEY_PREFERENCES.PROGRESS);
      return progress || {};
    } catch (error) {
      console.error('Failed to load journey progress from user preferences:', error);
      return {};
    }
  },

  // Journey Configuration Persistence
  async saveJourneyConfig({ dispatch, rootGetters }: any, config: Partial<JourneyState['config']>) {
    try {
      const currentConfig = rootGetters['prefs/get'](JOURNEY_PREFERENCES.CONFIG) || {};
      const updatedConfig = { ...currentConfig, ...config };

      await dispatch('prefs/set', {
        key: JOURNEY_PREFERENCES.CONFIG,
        value: updatedConfig,
      }, { root: true });

      console.log('Journey config saved to user preferences');
    } catch (error) {
      console.error('Failed to save journey config:', error);
      throw error;
    }
  },

  async loadJourneyConfig({ rootGetters }: any) {
    try {
      return rootGetters['prefs/get'](JOURNEY_PREFERENCES.CONFIG) || {};
    } catch (error) {
      console.error('Failed to load journey config:', error);
      return {};
    }
  },

  // Journey Analytics Persistence
  async saveJourneyAnalytics({ dispatch, rootGetters }: any, analytics: any) {
    try {
      const currentAnalytics = rootGetters['prefs/get'](JOURNEY_PREFERENCES.ANALYTICS) || {};

      // Merge analytics data intelligently
      const updatedAnalytics = {
        journeyStartCount: { ...currentAnalytics.journeyStartCount, ...analytics.journeyStartCount },
        journeyCompletionCount: { ...currentAnalytics.journeyCompletionCount, ...analytics.journeyCompletionCount },
        journeySkipCount: { ...currentAnalytics.journeySkipCount, ...analytics.journeySkipCount },
        averageCompletionTime: { ...currentAnalytics.averageCompletionTime, ...analytics.averageCompletionTime },
        dropOffPoints: { ...currentAnalytics.dropOffPoints, ...analytics.dropOffPoints },
        popularChoices: { ...currentAnalytics.popularChoices, ...analytics.popularChoices },
        userPaths: [...(currentAnalytics.userPaths || []), ...(analytics.userPaths || [])],
      };

      await dispatch('prefs/set', {
        key: JOURNEY_PREFERENCES.ANALYTICS,
        value: updatedAnalytics,
      }, { root: true });

      console.log('Journey analytics saved to user preferences');
    } catch (error) {
      console.error('Failed to save journey analytics:', error);
      throw error;
    }
  },

  async loadJourneyAnalytics({ rootGetters }: any) {
    try {
      return rootGetters['prefs/get'](JOURNEY_PREFERENCES.ANALYTICS) || {
        journeyStartCount: {},
        journeyCompletionCount: {},
        journeySkipCount: {},
        averageCompletionTime: {},
        dropOffPoints: {},
        popularChoices: {},
        userPaths: [],
      };
    } catch (error) {
      console.error('Failed to load journey analytics:', error);
      return {
        journeyStartCount: {},
        journeyCompletionCount: {},
        journeySkipCount: {},
        averageCompletionTime: {},
        dropOffPoints: {},
        popularChoices: {},
        userPaths: [],
      };
    }
  },

  // System State Persistence
  async saveSystemState({ dispatch, rootGetters }: any, systemState: Record<string, any>) {
    try {
      const currentState = rootGetters['prefs/get'](JOURNEY_PREFERENCES.SYSTEM_STATE) || {};
      const updatedState = { ...currentState, ...systemState };

      await dispatch('prefs/set', {
        key: JOURNEY_PREFERENCES.SYSTEM_STATE,
        value: updatedState,
      }, { root: true });

      console.log('Journey system state saved to user preferences');
    } catch (error) {
      console.error('Failed to save journey system state:', error);
      throw error;
    }
  },

  async loadSystemState({ rootGetters }: any) {
    try {
      return rootGetters['prefs/get'](JOURNEY_PREFERENCES.SYSTEM_STATE) || {};
    } catch (error) {
      console.error('Failed to load journey system state:', error);
      return {};
    }
  },

  // UI State Persistence
  async saveUIState({ dispatch, rootGetters }: any, uiState: any) {
    try {
      const currentUIState = rootGetters['prefs/get'](JOURNEY_PREFERENCES.UI_STATE) || {};
      const updatedUIState = { ...currentUIState, ...uiState };

      await dispatch('prefs/set', {
        key: JOURNEY_PREFERENCES.UI_STATE,
        value: updatedUIState,
      }, { root: true });

      console.log('Journey UI state saved to user preferences');
    } catch (error) {
      console.error('Failed to save journey UI state:', error);
      throw error;
    }
  },

  async loadUIState({ rootGetters }: any) {
    try {
      return rootGetters['prefs/get'](JOURNEY_PREFERENCES.UI_STATE) || {};
    } catch (error) {
      console.error('Failed to load journey UI state:', error);
      return {};
    }
  },

  // Completed Journeys Persistence
  async saveCompletedJourneys({ dispatch, rootGetters }: any, completedJourneys: string[]) {
    try {
      await dispatch('prefs/set', {
        key: JOURNEY_PREFERENCES.COMPLETED_JOURNEYS,
        value: completedJourneys,
      }, { root: true });

      console.log('Completed journeys saved to user preferences');
    } catch (error) {
      console.error('Failed to save completed journeys:', error);
      throw error;
    }
  },

  async loadCompletedJourneys({ rootGetters }: any) {
    try {
      return rootGetters['prefs/get'](JOURNEY_PREFERENCES.COMPLETED_JOURNEYS) || [];
    } catch (error) {
      console.error('Failed to load completed journeys:', error);
      return [];
    }
  },

  // Initialize Journey System with Saved Preferences
  async initializeFromPreferences({ dispatch, commit }: any) {
    try {
      console.log('Initializing journey system from user preferences...');

      // Load all saved data
      const [progress, config, analytics, systemState, uiState, completedJourneys] = await Promise.all([
        dispatch('loadFromUserPreferences'),
        dispatch('loadJourneyConfig'),
        dispatch('loadJourneyAnalytics'),
        dispatch('loadSystemState'),
        dispatch('loadUIState'),
        dispatch('loadCompletedJourneys'),
      ]);

      // Update store state with loaded data
      if (progress && Object.keys(progress).length > 0) {
        Object.keys(progress).forEach(journeyId => {
          commit('SET_USER_PROGRESS', { journeyId, progress: progress[journeyId] });
        });
      }

      if (config && Object.keys(config).length > 0) {
        commit('SET_CONFIG', config);
      }

      if (systemState && Object.keys(systemState).length > 0) {
        commit('SET_SYSTEM_STATE', systemState);
      }

      console.log('Journey system initialized from user preferences');
      return {
        progress,
        config,
        analytics,
        systemState,
        uiState,
        completedJourneys,
      };
    } catch (error) {
      console.error('Failed to initialize journey system from preferences:', error);
      throw error;
    }
  },

  // Clear All Journey Data
  async clearAllJourneyData({ dispatch }: any) {
    try {
      console.log('Clearing all journey data from user preferences...');

      // Clear all journey preferences
      await Promise.all([
        dispatch('prefs/set', { key: JOURNEY_PREFERENCES.PROGRESS, value: {} }, { root: true }),
        dispatch('prefs/set', { key: JOURNEY_PREFERENCES.CONFIG, value: {
          enableJourneys: true,
          enableAnalytics: true,
          debugMode: false,
          autoTrigger: true,
          tourMode: false,
        } }, { root: true }),
        dispatch('prefs/set', { key: JOURNEY_PREFERENCES.ANALYTICS, value: {
          journeyStartCount: {},
          journeyCompletionCount: {},
          journeySkipCount: {},
          averageCompletionTime: {},
          dropOffPoints: {},
          popularChoices: {},
          userPaths: [],
        } }, { root: true }),
        dispatch('prefs/set', { key: JOURNEY_PREFERENCES.SYSTEM_STATE, value: {} }, { root: true }),
        dispatch('prefs/set', { key: JOURNEY_PREFERENCES.UI_STATE, value: {} }, { root: true }),
        dispatch('prefs/set', { key: JOURNEY_PREFERENCES.COMPLETED_JOURNEYS, value: [] }, { root: true }),
      ]);

      console.log('All journey data cleared from user preferences');
    } catch (error) {
      console.error('Failed to clear journey data:', error);
      throw error;
    }
  },

  async saveToLocalStorage({ state }: any, progress: JourneyProgress) {
    try {
      const existing = JSON.parse(localStorage.getItem('rancher-journey-progress') || '{}');
      existing[progress.journeyId] = progress;
      localStorage.setItem('rancher-journey-progress', JSON.stringify(existing));
    } catch (error) {
      console.warn('Failed to save journey progress to localStorage:', error);
    }
  },

  async loadFromLocalStorage() {
    try {
      return JSON.parse(localStorage.getItem('rancher-journey-progress') || '{}');
    } catch (error) {
      console.warn('Failed to load journey progress from localStorage:', error);
      return {};
    }
  },

  // System State Management
  updateSystemState({ commit }: any, state: Record<string, any>) {
    commit('UPDATE_SYSTEM_STATE', state);
  },

  async checkTriggers({ state, getters, dispatch }: any, { event, context }: { event: string, context?: any }) {
    if (!state.config.enableJourneys || !state.config.autoTrigger) return;

    const triggeredJourney = getters.shouldTriggerJourney(event, context);
    if (triggeredJourney && !state.isActive) {
      await dispatch('startJourney', { journeyId: triggeredJourney.id, context });
    }
  },

  // Demo Helper Actions
  async triggerJourneyManually({ dispatch, commit }: any, { journeyId, context }: { journeyId: string, context?: any }) {
    console.log('Manually triggering journey:', journeyId);
    try {
      await dispatch('startJourney', { journeyId, context });
      return true;
    } catch (error) {
      console.error('Failed to manually trigger journey:', error);
      commit('ADD_ERROR', error);
      throw error;
    }
  },

  async simulateFirstLogin({ commit, dispatch }: any) {
    console.log('Simulating first login for demo...');

    // Update system state to trigger first login journey
    const firstLoginState = {
      isFirstLogin: true,
      hasCompletedOnboarding: false,
      lastLogin: new Date(),
      clusterCount: 0,
      credentialCount: 0,
    };

    commit('UPDATE_SYSTEM_STATE', firstLoginState);

    // Check for triggers
    await dispatch('checkTriggers', {
      event: 'first-login',
      context: { simulated: true }
    });
  },

  async simulateClusterCreation({ commit, dispatch }: any, { clusterName = 'demo-cluster' } = {}) {
    console.log('Simulating cluster creation for demo...');

    // Update system state to simulate cluster creation
    const clusterState = {
      clusterCount: 1,
      lastCreatedResource: 'cluster',
      clusters: [{ id: 'demo-cluster-1', name: clusterName, state: 'active' }],
    };

    commit('UPDATE_SYSTEM_STATE', clusterState);

    // Check for triggers
    await dispatch('checkTriggers', {
      event: 'resource-created',
      context: { resourceType: 'cluster', resourceName: clusterName }
    });
  },

  // Configuration
  setConfig({ commit }: any, config: Partial<JourneyState['config']>) {
    commit('SET_JOURNEY_CONFIG', config);
  },

  // Utility Actions
  async buildJourneyContext({ state, rootState, rootGetters }: any, { journey, progress, context }: any): Promise<JourneyContext> {
    return {
      journey,
      progress,
      user: rootGetters['auth/user'],
      route: rootState.route?.current,
      store: rootState,
      systemState: state.systemState,
      ...context,
    };
  },

  emitJourneyEvent({ state }: any, payload: Omit<JourneyEventPayload, 'timestamp'>) {
    const event: JourneyEventPayload = {
      ...payload,
      timestamp: new Date(),
    };

    // Emit to analytics if enabled
    if (state.config.enableAnalytics) {
      // TODO: Send to analytics endpoint
      console.log('Journey Event:', event);
    }

    // Emit to Vue event bus or other listeners
    // TODO: Implement event emission system
  },

  // Cache Management
  clearCache({ commit }: any) {
    commit('CLEAR_CACHE');
  },

  // Error Handling
  clearErrors({ commit }: any) {
    commit('CLEAR_ERRORS');
  },

  // UI State Management
  setJourneyOverlayVisible({ commit }: any, visible: boolean) {
    commit('SET_JOURNEY_OVERLAY_VISIBLE', visible);
  },

  setTourMode({ commit }: any, tourMode: boolean) {
    commit('SET_TOUR_MODE', tourMode);
  },

  updateLastInteraction({ commit }: any) {
    commit('UPDATE_LAST_INTERACTION');
  },
};
